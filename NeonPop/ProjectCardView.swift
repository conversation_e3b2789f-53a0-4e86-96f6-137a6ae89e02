//
//  ProjectCardView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/16.
//

import SwiftUI

struct ProjectCardView: View {
    let project: Project
    let onTap: () -> Void
    let onDelete: () -> Void
    
    @State private var showingContextMenu = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 0) {
                // 缩略图区域
                thumbnailView
                
                // 信息区域
                infoView
            }
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.black.opacity(0.6))
                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
            )
            .shadow(color: Color.black.opacity(0.3), radius: 8, x: 0, y: 4)
        }
        .buttonStyle(PlainButtonStyle())
        .contextMenu {
            Button(action: onTap) {
                Label("编辑", systemImage: "pencil")
            }
            
            But<PERSON>(action: onDelete) {
                Label("删除", systemImage: "trash")
            }
        }
    }
    
    // 缩略图视图
    private var thumbnailView: some View {
        ZStack {
            // 背景
            RoundedRectangle(cornerRadius: 16)
                .fill(CyberPunkStyle.electricBlue)
                .frame(height: 160)
            
            // 缩略图或占位符
            if let thumbnailImage = project.thumbnailImage {
                Image(uiImage: thumbnailImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 160)
                    .clipped()
                    .cornerRadius(16, corners: [.topLeft, .topRight])
            } else {
                // 占位符
                VStack(spacing: 8) {
                    Image(systemName: "photo")
                        .font(.system(size: 40))
                        .foregroundColor(.white.opacity(0.6))
                    
                    Text("新项目")
                        .font(.system(size: 12))
                        .foregroundColor(.white.opacity(0.6))
                }
            }
            
            // 实时更新指示器
            VStack {
                HStack {
                    Spacer()
                    
                    HStack(spacing: 4) {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 6, height: 6)
                        Text("实时")
                            .font(.system(size: 10, weight: .medium))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.black.opacity(0.6))
                    )
                    .padding(.top, 8)
                    .padding(.trailing, 8)
                }
                
                Spacer()
            }
        }
    }
    
    // 信息视图
    private var infoView: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 标题
            Text(project.title)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.white)
                .lineLimit(1)
            
            // 详细信息
            HStack {
                // 画布比例
                HStack(spacing: 4) {
                    Image(systemName: "aspectratio")
                        .font(.system(size: 10))
                    Text(project.aspectRatio)
                        .font(.system(size: 10))
                }
                .foregroundColor(.white.opacity(0.6))
                
                Spacer()
                
                // 修改时间
                Text(timeAgoString(from: project.modifiedAt))
                    .font(.system(size: 10))
                    .foregroundColor(.white.opacity(0.6))
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 10)
    }
    
    // 计算时间差
    private func timeAgoString(from date: Date) -> String {
        let now = Date()
        let timeInterval = now.timeIntervalSince(date)
        
        if timeInterval < 60 {
            return "刚刚"
        } else if timeInterval < 3600 {
            let minutes = Int(timeInterval / 60)
            return "\(minutes)分钟前"
        } else if timeInterval < 86400 {
            let hours = Int(timeInterval / 3600)
            return "\(hours)小时前"
        } else if timeInterval < 604800 {
            let days = Int(timeInterval / 86400)
            return "\(days)天前"
        } else {
            let formatter = DateFormatter()
            formatter.dateFormat = "MM/dd"
            return formatter.string(from: date)
        }
    }
}
