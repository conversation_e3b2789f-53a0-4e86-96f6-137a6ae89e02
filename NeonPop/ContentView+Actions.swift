//
//  ContentView+Actions.swift
//  NeonPop
//
//  Created by late night king on 2025/6/16.
//

import SwiftUI
import PhotosUI
import Photos

extension ContentView {
    
    // 加载选中的照片
    func loadSelectedPhoto() async {
        guard let selectedPhoto = selectedPhoto else { return }

        do {
            if let data = try await selectedPhoto.loadTransferable(type: Data.self),
               let uiImage = UIImage(data: data) {
                await processor.addImageLayer(from: uiImage, name: "图层 \(processor.layers.count + 1)")
                if let newLayer = processor.layers.last {
                    undoManager.recordAction(.addLayer(newLayer))

                    // 触发自动保存
                    DispatchQueue.main.async {
                        self.triggerAutoSave(actionDescription: "添加图片图层")
                    }
                }
            }
        } catch {
            processor.errorMessage = "加载图像失败: \(error.localizedDescription)"
        }

        // 重要：重置选中的照片状态，允许重新选择同一张照片
        DispatchQueue.main.async {
            self.selectedPhoto = nil
        }
    }

    // 保存白板截图 - 高质量保存，修复坐标系统
    func saveCompositeImage() {
        // 使用高质量保存尺寸，但确保坐标系统正确
        let qualityMultiplier: CGFloat = 3.0 // 3倍显示尺寸，提供高质量
        let saveSize = CGSize(
            width: displayCanvasSize.width * qualityMultiplier,
            height: displayCanvasSize.height * qualityMultiplier
        )

        // 安全检查：如果尺寸过大，适当缩小但保持比例
        let maxPixels: CGFloat = 12_000_000 // 1200万像素
        let currentPixels = saveSize.width * saveSize.height

        let finalSaveSize: CGSize
        if currentPixels > maxPixels {
            let scaleFactor = sqrt(maxPixels / currentPixels)
            finalSaveSize = CGSize(
                width: saveSize.width * scaleFactor,
                height: saveSize.height * scaleFactor
            )
            print("保存尺寸过大，已缩放到: \(finalSaveSize)")
        } else {
            finalSaveSize = saveSize
        }

        print("保存画布尺寸: \(finalSaveSize)")
        print("显示画布尺寸: \(displayCanvasSize)")
        print("质量倍数: \(qualityMultiplier)")
        print("图层数量: \(processor.layers.count)")
        print("像素数: \(Int(finalSaveSize.width * finalSaveSize.height))")

        let renderer = UIGraphicsImageRenderer(size: finalSaveSize)
        let image = renderer.image { context in
            let rect = CGRect(origin: .zero, size: finalSaveSize)

            print("=== 开始保存图片 ===")
            print("保存画布尺寸: \(rect)")
            print("显示画布尺寸: \(displayCanvasSize)")
            print("质量倍数: \(qualityMultiplier)")
            print("图层数量: \(processor.layers.count)")

            // 绘制蓝色背景 - 与显示完全一致的直角矩形
            UIColor(CyberPunkStyle.electricBlue).setFill()
            UIRectFill(rect)

            print("✅ 背景绘制完成，颜色: \(CyberPunkStyle.electricBlue)")

            // 绘制所有可见图层，使用修正的坐标系统
            for (index, layer) in processor.layers.enumerated() where layer.isVisible {
                print("绘制图层 \(index): \(layer.name), 类型: \(layer.type), 位置: \(layer.transform.position)")
                drawLayerInHighQualityContext(layer, context: context, canvasRect: rect, qualityMultiplier: qualityMultiplier)
            }

            print("=== 保存图片完成 ===")
        }

        // 保存到相册
        PHPhotoLibrary.requestAuthorization { status in
            if status == .authorized {
                PHPhotoLibrary.shared().performChanges({
                    PHAssetChangeRequest.creationRequestForAsset(from: image)
                }) { success, error in
                    DispatchQueue.main.async {
                        if success {
                            showingSaveAlert = true
                            // 同时保存到历史记录
                            saveToHistory()
                        } else if let error = error {
                            processor.errorMessage = "保存失败: \(error.localizedDescription)"
                        }
                    }
                }
            }
        }
    }

    // 添加文字图层
    func addTextLayer() {
        let newLayer = LayerModel(name: "文字 \(processor.layers.count + 1)", type: .text)
        newLayer.text = "点击编辑文字"
        newLayer.textColor = .white
        newLayer.fontSize = 24
        newLayer.fontName = "System"
        newLayer.isBold = false
        newLayer.isItalic = false
        newLayer.isUnderlined = false
        newLayer.textAlignment = .center
        newLayer.isVertical = false

        processor.layers.append(newLayer)
        undoManager.recordAction(.addLayer(newLayer))

        Task {
            await processor.updateCompositeImage()
        }

        // 触发自动保存
        triggerAutoSave(actionDescription: "添加文字图层")
    }

    // 添加背景装饰文字图层
    func addDecorativeTextLayer() {
        let newLayer = LayerModel(name: "背景文字 \(processor.layers.count + 1)", type: .text)
        newLayer.text = "CYBER"
        newLayer.textColor = Color(CyberPunkStyle.decorativeBlue)
        newLayer.fontSize = 48
        newLayer.fontName = "System"
        newLayer.isBold = true
        newLayer.isItalic = false
        newLayer.isUnderlined = false
        newLayer.textAlignment = .center
        newLayer.isVertical = false
        newLayer.hasBlur = true
        newLayer.blurRadius = 5.0 // 默认高斯模糊效果
        newLayer.transform.opacity = 0.7 // 设置透明度

        processor.layers.append(newLayer)
        undoManager.recordAction(.addLayer(newLayer))

        Task {
            await processor.updateCompositeImage()
        }

        // 触发自动保存
        triggerAutoSave(actionDescription: "添加背景文字图层")
    }

    // 添加标题文字图层
    func addTitleTextLayer() {
        let newLayer = LayerModel(name: "标题文字 \(processor.layers.count + 1)", type: .text)
        newLayer.text = "NEON POP"
        newLayer.textColor = Color(CyberPunkStyle.neonPink)
        newLayer.fontSize = 36
        newLayer.fontName = "System"
        newLayer.isBold = true
        newLayer.isItalic = false
        newLayer.isUnderlined = false
        newLayer.textAlignment = .center
        newLayer.isVertical = false
        // 粉色标题文字不使用模糊效果
        newLayer.hasBlur = false
        newLayer.blurRadius = 0

        processor.layers.append(newLayer)
        undoManager.recordAction(.addLayer(newLayer))

        Task {
            await processor.updateCompositeImage()
        }

        // 触发自动保存
        triggerAutoSave(actionDescription: "添加标题文字图层")
    }

    // 获取选中的图层
    func getSelectedLayer() -> LayerModel? {
        return processor.layers.first { $0.isSelected }
    }

    // 更新画布尺寸 - 确保画布始终居中
    func updateCanvasSize() {
        // 获取屏幕尺寸
        let screenSize = UIScreen.main.bounds.size
        let safeAreaInsets = UIApplication.shared.windows.first?.safeAreaInsets ?? UIEdgeInsets.zero

        // 计算可用空间 - 优化空间分配
        let horizontalPadding: CGFloat = 40 // 左右边距

        // 顶部空间：工具栏(50) + 标题(有图层时0，无图层时60) + 间距(15)
        // 安全获取图层数量
        let layerCount = getLayerCount()
        let titleHeight: CGFloat = layerCount == 0 ? 60 : 0
        let topSpace: CGFloat = 50 + titleHeight + 15 + safeAreaInsets.top

        // 底部空间：按钮(80) + 间距(20) - 图层控制按钮现在是悬浮的，不占用空间
        let bottomSpace: CGFloat = 80 + 20 + safeAreaInsets.bottom

        let availableWidth = screenSize.width - horizontalPadding
        let availableHeight = screenSize.height - topSpace - bottomSpace

        // 根据选择的比例计算画布尺寸
        let aspectRatio = selectedAspectRatio.ratio

        // 计算屏幕显示尺寸 - 确保画布完全适配可用空间并居中
        var displayWidth: CGFloat
        var displayHeight: CGFloat

        // 按宽度和高度分别计算，取较小的缩放比例以确保完全适配
        let widthBasedHeight = availableWidth / aspectRatio
        let heightBasedWidth = availableHeight * aspectRatio

        if widthBasedHeight <= availableHeight {
            // 以宽度为限制
            displayWidth = availableWidth
            displayHeight = widthBasedHeight
        } else {
            // 以高度为限制
            displayWidth = heightBasedWidth
            displayHeight = availableHeight
        }

        // 确保最小尺寸，避免画布过小
        let minSize: CGFloat = 200
        if displayWidth < minSize || displayHeight < minSize {
            let scale = max(minSize / displayWidth, minSize / displayHeight)
            displayWidth *= scale
            displayHeight *= scale
        }

        // 设置显示尺寸
        displayCanvasSize = CGSize(width: displayWidth, height: displayHeight)

        // 设置保存尺寸 - 确保至少等于显示尺寸，保证画板完整性
        let maxSaveWidth: CGFloat = 2160 // 提高最大宽度限制
        let maxSaveHeight: CGFloat = 2880 // 提高最大高度限制

        // 计算理想的保存尺寸（1.5倍显示尺寸，提供更好质量）
        let idealSaveWidth = displayWidth * 1.5
        let idealSaveHeight = displayHeight * 1.5

        // 只有在理想尺寸超过限制时才进行缩放，否则保持1.5倍
        let finalSaveWidth: CGFloat
        let finalSaveHeight: CGFloat

        if idealSaveWidth > maxSaveWidth || idealSaveHeight > maxSaveHeight {
            // 需要缩放以适应限制
            let scaleFactorForWidth = maxSaveWidth / displayWidth
            let scaleFactorForHeight = maxSaveHeight / displayHeight
            let scaleFactor = min(scaleFactorForWidth, scaleFactorForHeight, 1.5)

            finalSaveWidth = displayWidth * max(scaleFactor, 1.0) // 确保至少等于显示尺寸
            finalSaveHeight = displayHeight * max(scaleFactor, 1.0)
        } else {
            // 使用理想尺寸
            finalSaveWidth = idealSaveWidth
            finalSaveHeight = idealSaveHeight
        }

        canvasSize = CGSize(width: finalSaveWidth, height: finalSaveHeight)

        print("更新画布尺寸 - 显示: \(displayCanvasSize), 保存: \(canvasSize), 比例: \(selectedAspectRatio.rawValue)")
        print("屏幕可用空间: \(availableWidth) x \(availableHeight)")
        print("顶部空间: \(topSpace), 底部空间: \(bottomSpace) (图层控制按钮悬浮，不占用空间)")
        print("安全区域: top=\(safeAreaInsets.top), bottom=\(safeAreaInsets.bottom)")
    }

    // 检查是否可以上移选中的图层
    func canMoveSelectedLayerUp() -> Bool {
        guard let selectedLayer = processor.layers.first(where: { $0.isSelected }),
              let currentIndex = processor.layers.firstIndex(where: { $0.id == selectedLayer.id }) else {
            return false
        }
        return currentIndex < processor.layers.count - 1
    }

    // 检查是否可以下移选中的图层
    func canMoveSelectedLayerDown() -> Bool {
        guard let selectedLayer = processor.layers.first(where: { $0.isSelected }),
              let currentIndex = processor.layers.firstIndex(where: { $0.id == selectedLayer.id }) else {
            return false
        }
        return currentIndex > 0
    }

    // 保存当前项目到历史记录
    func saveToHistory(title: String? = nil) {
        let historyManager = HistoryManager()
        let projectTitle = title ?? historyManager.generateDefaultTitle()
        let thumbnailImage = generateCanvasImage()

        historyManager.saveHistory(
            title: projectTitle,
            layers: processor.layers,
            canvasSize: canvasSize,
            aspectRatio: selectedAspectRatio,
            thumbnailImage: thumbnailImage
        )

        // 发送保存通知
        NotificationCenter.default.post(name: .init("SaveProject"), object: nil)
    }

    // 触发自动保存
    func triggerAutoSave(actionDescription: String) {
        guard let projectManager = projectManager,
              let currentProject = currentProject else {
            print("⚠️ 自动保存失败: projectManager或currentProject为nil")
            print("   projectManager存在: \(projectManager != nil)")
            print("   currentProject存在: \(currentProject != nil)")
            return
        }

        print("🔄 开始自动保存: \(actionDescription)")
        print("   项目标题: \(currentProject.title)")
        print("   项目ID: \(currentProject.id)")
        print("   图层数量: \(processor.layers.count)")
        print("   画布尺寸: \(canvasSize)")

        // 打印图层详情
        for (index, layer) in processor.layers.enumerated() {
            print("   图层\(index): \(layer.name) (类型: \(layer.type), 可见: \(layer.isVisible))")
        }

        // 保存编辑步骤
        projectManager.saveEditStep(
            for: currentProject.id,
            actionDescription: actionDescription,
            layers: processor.layers,
            canvasSize: canvasSize,
            aspectRatio: selectedAspectRatio
        )

        // 更新项目缩略图
        let thumbnailImage = generateCanvasImage()
        projectManager.updateProjectThumbnail(projectId: currentProject.id, image: thumbnailImage)

        print("✅ 自动保存完成: \(actionDescription)")

        // 验证保存结果
        let savedSteps = projectManager.getEditSteps(for: currentProject.id)
        print("   验证: 项目现在有 \(savedSteps.count) 个编辑步骤")
    }

    // 安全访问StateObject的辅助方法
    func getLayerCount() -> Int {
        return processor.layers.count
    }

    func getLayersSafely() -> [LayerModel] {
        return processor.layers
    }

    func updateCompositeImageSafely() async {
        await processor.updateCompositeImage()
    }

    // 调试方法：测试保存和加载
    func debugSaveAndLoad() {
        guard let projectManager = projectManager,
              let currentProject = currentProject else {
            print("❌ 调试失败: projectManager或currentProject为nil")
            return
        }

        print("🔍 开始调试保存和加载")
        print("   当前项目: \(currentProject.title)")
        print("   当前图层数量: \(processor.layers.count)")

        // 保存当前状态
        triggerAutoSave(actionDescription: "调试保存")

        // 等待一下，然后尝试加载
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let steps = projectManager.getEditSteps(for: currentProject.id)
            print("   保存后的步骤数量: \(steps.count)")

            if let latestStep = steps.last {
                print("   最新步骤: \(latestStep.actionDescription)")
                print("   步骤中的图层数量: \(latestStep.layers.count)")

                // 尝试恢复图层
                let restoredLayers = latestStep.restoreLayers()
                print("   恢复的图层数量: \(restoredLayers.count)")

                for (index, layer) in restoredLayers.enumerated() {
                    print("   恢复图层\(index): \(layer.name) (\(layer.type))")
                    if layer.type == .text {
                        print("     文字内容: \(layer.text ?? "nil")")
                    }
                }
            }
        }
    }
}
