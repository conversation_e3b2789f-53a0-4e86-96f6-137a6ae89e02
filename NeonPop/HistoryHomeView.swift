//
//  HistoryHomeView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/16.
//

import SwiftUI

struct HistoryHomeView: View {
    @StateObject private var projectManager = ProjectManager()
    @State private var searchText = ""
    @State private var showingNewProject = false
    @State private var selectedProject: Project?
    @State private var showingDeleteAlert = false
    @State private var projectToDelete: Project?
    
    private let columns = [
        GridItem(.flexible(), spacing: 12),
        GridItem(.flexible(), spacing: 12)
    ]
    
    var filteredProjects: [Project] {
        if searchText.isEmpty {
            return projectManager.projects
        }
        return projectManager.projects.filter { project in
            project.title.localizedCaseInsensitiveContains(searchText)
        }
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景渐变
                LinearGradient(
                    colors: [Color.black, CyberPunkStyle.electricBlue.opacity(0.2)],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // 顶部标题和搜索
                    headerView
                    
                    // 内容区域
                    if filteredProjects.isEmpty {
                        emptyStateView
                    } else {
                        projectGridView
                    }
                }
            }
            .navigationBarHidden(true)
        }

        .sheet(item: $selectedProject) { project in
            ProjectEditingView(project: project, projectManager: projectManager)
        }
        .alert("删除作品", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                if let project = projectToDelete {
                    projectManager.deleteProject(project)
                }
            }
        } message: {
            Text("确定要删除这个作品吗？此操作无法撤销。")
        }
    }
    
    // 顶部标题和搜索栏
    private var headerView: some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    NeonTextView(
                        text: "NEON POP",
                        color: CyberPunkStyle.neonPink,
                        font: .system(size: 28, weight: .heavy),
                        glowRadius: 10
                    )
                    
                    Text("我的作品")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
                
                Spacer()
                
                // 新建按钮
                Button(action: {
                    let newProject = projectManager.createNewProject()
                    selectedProject = newProject
                }) {
                    Image(systemName: "plus")
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(width: 44, height: 44)
                        .background(
                            Circle()
                                .fill(CyberPunkStyle.neonPink)
                                .shadow(color: CyberPunkStyle.neonPink.opacity(0.5), radius: 8)
                        )
                }
            }
            
            // 搜索栏
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.white.opacity(0.6))
                
                TextField("搜索作品...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                    .foregroundColor(.white)
                
                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.white.opacity(0.6))
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.1))
                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
            )
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
    }
    
    // 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "photo.artframe")
                .font(.system(size: 80))
                .foregroundColor(.white.opacity(0.3))
            
            VStack(spacing: 12) {
                Text(searchText.isEmpty ? "还没有作品" : "没有找到相关作品")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white.opacity(0.8))
                
                Text(searchText.isEmpty ? "点击右上角 + 号开始创作第一个作品" : "尝试使用其他关键词搜索")
                    .font(.system(size: 14))
                    .foregroundColor(.white.opacity(0.6))
                    .multilineTextAlignment(.center)
            }
            
            if searchText.isEmpty {
                Button(action: {
                    let newProject = projectManager.createNewProject()
                    selectedProject = newProject
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "plus")
                        Text("开始创作")
                    }
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(CyberPunkStyle.neonPink)
                            .shadow(color: CyberPunkStyle.neonPink.opacity(0.5), radius: 8)
                    )
                }
            }
            
            Spacer()
        }
        .padding(.horizontal, 40)
    }
    
    // 项目网格视图
    private var projectGridView: some View {
        ScrollView {
            LazyVGrid(columns: columns, spacing: 16) {
                ForEach(filteredProjects) { project in
                    ProjectCardView(
                        project: project,
                        onTap: {
                            selectedProject = project
                        },
                        onDelete: {
                            projectToDelete = project
                            showingDeleteAlert = true
                        }
                    )
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            .padding(.bottom, 40)
        }
    }
}
