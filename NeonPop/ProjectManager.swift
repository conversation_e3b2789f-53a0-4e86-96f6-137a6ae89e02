//
//  ProjectManager.swift
//  NeonPop
//
//  Created by late night king on 2025/6/16.
//

import SwiftUI
import Foundation
import UIKit

// 项目数据模型
struct Project: Identifiable, Codable {
    let id = UUID()
    var title: String
    let createdAt: Date
    var modifiedAt: Date
    var thumbnailImageData: Data?
    var canvasSize: CGSize
    var aspectRatio: String
    var currentStepIndex: Int = 0 // 当前步骤索引
    
    init(title: String, canvasSize: CGSize, aspectRatio: AspectRatio) {
        self.title = title
        self.createdAt = Date()
        self.modifiedAt = Date()
        self.canvasSize = canvasSize
        self.aspectRatio = aspectRatio.rawValue
    }
    
    // 获取缩略图
    var thumbnailImage: UIImage? {
        guard let data = thumbnailImageData else { return nil }
        return UIImage(data: data)
    }
    
    // 更新缩略图
    mutating func updateThumbnail(_ image: UIImage) {
        self.thumbnailImageData = image.jpegData(compressionQuality: 0.8)
        self.modifiedAt = Date()
    }
}

// 编辑步骤数据模型
struct EditStep: Identifiable, Codable {
    let id = UUID()
    let projectId: UUID
    let stepIndex: Int
    let timestamp: Date
    let actionDescription: String
    let layers: [EditHistory.LayerData] // 重用 EditHistory 的 LayerData
    let canvasSize: CGSize
    let aspectRatio: String
    
    init(projectId: UUID, stepIndex: Int, actionDescription: String, layers: [LayerModel], canvasSize: CGSize, aspectRatio: AspectRatio) {
        self.projectId = projectId
        self.stepIndex = stepIndex
        self.timestamp = Date()
        self.actionDescription = actionDescription
        self.canvasSize = canvasSize
        self.aspectRatio = aspectRatio.rawValue
        
        // 转换图层为可保存的数据
        self.layers = layers.map { layer in
            EditHistory.LayerData(
                id: layer.id,
                name: layer.name,
                type: EditHistory.LayerData.LayerType(rawValue: layer.type.rawValue) ?? .image,
                isVisible: layer.isVisible,
                isSelected: layer.isSelected,
                transform: EditHistory.LayerData.LayerTransform(
                    position: layer.transform.position,
                    scale: layer.transform.scale,
                    rotation: layer.transform.rotation,
                    opacity: layer.transform.opacity
                ),
                originalImageData: layer.originalImage?.jpegData(compressionQuality: 0.3),
                processedImageData: layer.processedImage?.jpegData(compressionQuality: 0.3),
                isColorTransformed: layer.isColorTransformed,
                text: layer.text,
                textColor: EditHistory.LayerData.CodableColor(color: layer.textColor),
                fontSize: layer.fontSize,
                fontName: layer.fontName,
                isBold: layer.isBold,
                isItalic: layer.isItalic,
                isUnderlined: layer.isUnderlined,
                textAlignment: EditHistory.LayerData.TextAlignment(rawValue: layer.textAlignment.rawValue) ?? .center,
                isVertical: layer.isVertical,
                hasBlur: layer.hasBlur,
                blurRadius: layer.blurRadius
            )
        }
    }
    
    // 恢复图层
    func restoreLayers() -> [LayerModel] {
        return layers.map { layerData in
            let layer = LayerModel(id: layerData.id, name: layerData.name, type: LayerType(rawValue: layerData.type.rawValue) ?? .image)
            layer.isVisible = layerData.isVisible
            layer.isSelected = layerData.isSelected
            layer.transform.position = layerData.transform.position
            layer.transform.scale = layerData.transform.scale
            layer.transform.rotation = layerData.transform.rotation
            layer.transform.opacity = layerData.transform.opacity
            layer.isColorTransformed = layerData.isColorTransformed
            
            // 恢复图像数据
            if let originalImageData = layerData.originalImageData {
                layer.originalImage = UIImage(data: originalImageData)
            }
            if let processedImageData = layerData.processedImageData {
                layer.processedImage = UIImage(data: processedImageData)
            }
            
            // 恢复文字属性
            layer.text = layerData.text
            layer.textColor = layerData.textColor?.color ?? .white
            layer.fontSize = layerData.fontSize
            layer.fontName = layerData.fontName
            layer.isBold = layerData.isBold
            layer.isItalic = layerData.isItalic
            layer.isUnderlined = layerData.isUnderlined
            layer.textAlignment = CustomTextAlignment(rawValue: layerData.textAlignment.rawValue) ?? .center
            layer.isVertical = layerData.isVertical
            layer.hasBlur = layerData.hasBlur
            layer.blurRadius = layerData.blurRadius
            
            return layer
        }
    }
}

// 项目管理器
class ProjectManager: ObservableObject {
    @Published var projects: [Project] = []
    @Published var currentProject: Project?
    
    private let userDefaults = UserDefaults.standard
    private let projectsKey = "NeonPop_Projects"
    private let stepsKey = "NeonPop_EditSteps"
    
    // 编辑步骤存储（按项目ID分组）
    private var editSteps: [UUID: [EditStep]] = [:]
    private let maxStepsPerProject = 30
    
    init() {
        // 清理旧的数据结构
        cleanupOldData()

        loadProjects()
        loadEditSteps()
    }

    // 清理旧的数据结构
    private func cleanupOldData() {
        // 删除旧的EditHistories数据
        if userDefaults.object(forKey: "NeonPop_EditHistories") != nil {
            print("🧹 清理旧的EditHistories数据")
            userDefaults.removeObject(forKey: "NeonPop_EditHistories")
        }

        // 检查数据大小，如果太大则清理
        if let stepsData = userDefaults.data(forKey: stepsKey),
           stepsData.count > 1024 * 1024 { // 超过1MB
            print("🧹 编辑步骤数据过大(\(stepsData.count)字节)，清理中...")
            userDefaults.removeObject(forKey: stepsKey)
        }
    }
    
    // 创建新项目
    func createNewProject() -> Project {
        let title = generateDefaultTitle()
        var project = Project(
            title: title,
            canvasSize: CGSize(width: 375, height: 500),
            aspectRatio: .ratio3x4
        )

        print("🆕 创建新项目: \(title)")
        print("   项目ID: \(project.id)")
        print("   画布尺寸: \(project.canvasSize)")

        // 立即生成基础缩略图（蓝色背景）
        let baseThumbnail = generateBaseThumbnail(size: project.canvasSize)
        project.updateThumbnail(baseThumbnail)
        print("   ✅ 基础缩略图已生成")

        projects.insert(project, at: 0)
        currentProject = project
        print("   ✅ 项目已添加到列表，设为当前项目")

        // 创建初始步骤
        saveEditStep(
            for: project.id,
            actionDescription: "创建项目",
            layers: [],
            canvasSize: project.canvasSize,
            aspectRatio: AspectRatio(rawValue: project.aspectRatio) ?? .ratio3x4
        )

        saveProjects()
        print("   ✅ 项目创建完成并保存")
        return project
    }

    // 生成基础缩略图（蓝色背景）
    private func generateBaseThumbnail(size: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { context in
            let rect = CGRect(origin: .zero, size: size)

            // 绘制蓝色背景，使用与CyberPunkStyle一致的颜色
            UIColor(CyberPunkStyle.electricBlue).setFill()
            UIRectFill(rect)
        }
    }
    
    // 保存编辑步骤
    func saveEditStep(for projectId: UUID, actionDescription: String, layers: [LayerModel], canvasSize: CGSize, aspectRatio: AspectRatio) {
        let currentSteps = editSteps[projectId] ?? []
        let stepIndex = currentSteps.count

        print("💾 保存编辑步骤: \(actionDescription)")
        print("   项目ID: \(projectId)")
        print("   步骤索引: \(stepIndex)")
        print("   图层数量: \(layers.count)")
        print("   画布尺寸: \(canvasSize)")

        // 详细打印每个要保存的图层信息
        for (index, layer) in layers.enumerated() {
            print("   保存图层\(index): \(layer.name)")
            print("     类型: \(layer.type)")
            print("     可见: \(layer.isVisible)")
            if layer.type == .text {
                print("     文字: \(layer.text ?? "nil")")
                print("     字体大小: \(layer.fontSize)")
                print("     颜色: \(layer.textColor)")
            }
            if layer.type == .image {
                print("     原始图片: \(layer.originalImage != nil ? "存在" : "nil")")
                print("     处理图片: \(layer.processedImage != nil ? "存在" : "nil")")
            }
        }

        let step = EditStep(
            projectId: projectId,
            stepIndex: stepIndex,
            actionDescription: actionDescription,
            layers: layers,
            canvasSize: canvasSize,
            aspectRatio: aspectRatio
        )

        var updatedSteps = currentSteps
        updatedSteps.append(step)

        // 限制步骤数量为30个
        if updatedSteps.count > maxStepsPerProject {
            updatedSteps = Array(updatedSteps.suffix(maxStepsPerProject))
            // 注意：我们保持原始的 stepIndex，不需要重新索引
            // 因为 stepIndex 主要用于标识步骤的顺序，而不是数组索引
        }

        editSteps[projectId] = updatedSteps

        // 更新项目的当前步骤索引
        if var project = currentProject, project.id == projectId {
            project.currentStepIndex = updatedSteps.count - 1
            currentProject = project
            updateProject(project)
        }

        saveEditSteps()
        print("   ✅ 编辑步骤保存完成，总步骤数: \(updatedSteps.count)")
    }
    
    // 获取项目的编辑步骤
    func getEditSteps(for projectId: UUID) -> [EditStep] {
        return editSteps[projectId] ?? []
    }
    
    // 获取特定步骤
    func getEditStep(for projectId: UUID, at index: Int) -> EditStep? {
        let steps = editSteps[projectId] ?? []
        guard index >= 0 && index < steps.count else { return nil }
        return steps[index]
    }
    
    // 更新项目
    func updateProject(_ project: Project) {
        if let index = projects.firstIndex(where: { $0.id == project.id }) {
            projects[index] = project
            saveProjects()
        }
    }
    
    // 更新项目缩略图
    func updateProjectThumbnail(projectId: UUID, image: UIImage) {
        if let index = projects.firstIndex(where: { $0.id == projectId }) {
            projects[index].updateThumbnail(image)
            if currentProject?.id == projectId {
                currentProject = projects[index]
            }
            saveProjects()
        }
    }
    
    // 删除项目
    func deleteProject(_ project: Project) {
        projects.removeAll { $0.id == project.id }
        editSteps.removeValue(forKey: project.id)
        if currentProject?.id == project.id {
            currentProject = nil
        }
        saveProjects()
        saveEditSteps()
    }
    
    // 生成默认标题
    private func generateDefaultTitle() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd HH:mm"
        return "作品 \(formatter.string(from: Date()))"
    }
    
    // 保存项目到本地存储
    private func saveProjects() {
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(projects)
            userDefaults.set(data, forKey: projectsKey)
        } catch {
            print("保存项目失败: \(error)")
        }
    }
    
    // 从本地存储加载项目
    private func loadProjects() {
        guard let data = userDefaults.data(forKey: projectsKey) else { return }
        
        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            projects = try decoder.decode([Project].self, from: data)
        } catch {
            print("加载项目失败: \(error)")
            projects = []
        }
    }
    
    // 保存编辑步骤到本地存储
    private func saveEditSteps() {
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(editSteps)
            userDefaults.set(data, forKey: stepsKey)
        } catch {
            print("保存编辑步骤失败: \(error)")
        }
    }
    
    // 从本地存储加载编辑步骤
    private func loadEditSteps() {
        guard let data = userDefaults.data(forKey: stepsKey) else { return }
        
        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            editSteps = try decoder.decode([UUID: [EditStep]].self, from: data)
        } catch {
            print("加载编辑步骤失败: \(error)")
            editSteps = [:]
        }
    }
}
