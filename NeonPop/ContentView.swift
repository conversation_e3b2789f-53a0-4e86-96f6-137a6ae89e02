//
//  ContentView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/16.
//

import SwiftUI
import PhotosUI
import Photos

struct ContentView: View {
    @StateObject var processor = MultiLayerProcessor()
    @StateObject var undoManager = UndoRedoManager()
    @State var selectedPhoto: PhotosPickerItem?
    @State var showingImagePicker = false
    @State var showingSaveAlert = false
    @State var showingShareSheet = false
    @State var showingHelp = false
    @State var showingTextEditor = false
    @State var editingTextLayer: LayerModel?
    @State var canvasSize = CGSize(width: 375, height: 500) // 保存时的实际尺寸
    @State var displayCanvasSize = CGSize(width: 375, height: 500) // 屏幕显示尺寸
    @State var showingAspectRatioMenu = false
    @State var selectedAspectRatio: AspectRatio = .ratio3x4
    @State var isResizingCanvas = false // 跟踪画布是否正在调整尺寸

    // 新增：用于控制是否显示返回按钮
    @Environment(\.presentationMode) var presentationMode
    @State var showBackButton = true

    // 项目管理相关属性 - 改为非可选类型，通过初始化传入
    var projectManager: ProjectManager
    var currentProject: Project

    // 初始化方法
    init(showBackButton: Bool = true, projectManager: ProjectManager? = nil, currentProject: Project? = nil) {
        self._showBackButton = State(initialValue: showBackButton)
        self.projectManager = projectManager ?? ProjectManager()
        self.currentProject = currentProject ?? Project(title: "默认项目", canvasSize: CGSize(width: 375, height: 500), aspectRatio: .ratio3x4)

        print("🔧 ContentView初始化")
        print("   ProjectManager: \(self.projectManager)")
        print("   CurrentProject: \(self.currentProject.title)")
    }

    // 恢复项目数据
    private func restoreProjectData() {
        print("📂 ContentView恢复项目数据: \(currentProject.title)")

        // 获取最新的编辑步骤
        let steps = projectManager.getEditSteps(for: currentProject.id)
        print("   找到 \(steps.count) 个编辑步骤")

        if let latestStep = steps.last {
            print("   恢复最新步骤: \(latestStep.actionDescription)")
            print("   步骤图层数量: \(latestStep.layers.count)")

            // 恢复图层
            let restoredLayers = latestStep.restoreLayers()

            // 确保所有图层都不被选中
            for layer in restoredLayers {
                layer.isSelected = false
            }

            processor.layers = restoredLayers
            print("   ✅ 图层恢复完成，实际图层数量: \(restoredLayers.count)")

            // 恢复画布设置
            canvasSize = latestStep.canvasSize
            displayCanvasSize = latestStep.canvasSize

            if let aspectRatio = AspectRatio.allCases.first(where: { $0.rawValue == latestStep.aspectRatio }) {
                selectedAspectRatio = aspectRatio
            }
            print("   ✅ 画布设置恢复完成: \(latestStep.canvasSize)")
        } else {
            print("   没有编辑步骤，使用项目基本设置")
            // 使用项目的基本设置
            canvasSize = currentProject.canvasSize
            displayCanvasSize = currentProject.canvasSize

            if let aspectRatio = AspectRatio.allCases.first(where: { $0.rawValue == currentProject.aspectRatio }) {
                selectedAspectRatio = aspectRatio
            }

            // 清空图层
            processor.layers = []
            print("   ✅ 空白项目设置完成")
        }

        // 更新画布尺寸
        updateCanvasSize()

        // 更新合成图像
        Task {
            await processor.updateCompositeImage()
        }

        print("📂 ContentView项目数据恢复完成")
    }



    var body: some View {
        GeometryReader { geometry in
            // 背景渐变
            LinearGradient(
                colors: [Color.black, CyberPunkStyle.electricBlue.opacity(0.3)],
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()
            .overlay(
                VStack(spacing: 0) {
                    // 顶部工具栏 - 撤销/重做/保存
                    topToolbar
                        .padding(.top, geometry.safeAreaInsets.top + 5)
                        .padding(.horizontal)

                    // 应用标题 - 只在没有图层时显示
                    if processor.layers.isEmpty {
                        VStack(spacing: 8) {
                            NeonTextView(
                                text: "NEON POP",
                                color: CyberPunkStyle.neonPink,
                                font: .system(size: 32, weight: .heavy),
                                glowRadius: 12
                            )

                            // 副标题
                            Text("赛博朋克风格滤镜")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white.opacity(0.8))
                        }
                        .padding(.top, 8)
                    }

                    // 白板编辑区域 - 真正居中显示
                    canvasArea
                        .padding(.top, processor.layers.isEmpty ? 15 : 8)
                        .padding(.horizontal, 20)
                        .onChange(of: processor.layers.count) { _ in
                            // 当图层数量变化时重新计算画布尺寸（主要影响标题显示）
                            updateCanvasSize()
                        }

                    // 错误信息和抠图状态提示
                    if let errorMessage = processor.errorMessage {
                        HStack(spacing: 8) {
                            Image(systemName: processor.lastCutoutFailed ? "exclamationmark.triangle.fill" : "info.circle.fill")
                                .foregroundColor(processor.lastCutoutFailed ? .orange : .red)

                            Text(errorMessage)
                                .font(.system(size: 12))
                                .foregroundColor(.white)

                            if processor.lastCutoutFailed {
                                Button("重试") {
                                    if let failedLayer = processor.lastFailedLayer {
                                        Task {
                                            await processor.retryBackgroundRemoval(for: failedLayer)
                                        }
                                    }
                                }
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(CyberPunkStyle.neonPink)
                            }

                            Button("关闭") {
                                processor.errorMessage = nil
                                processor.lastCutoutFailed = false
                                processor.lastFailedLayer = nil
                            }
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white.opacity(0.7))
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.black.opacity(0.8))
                        .cornerRadius(8)
                        .padding(.horizontal)
                        .padding(.top, 5)
                    }

                    // 底部功能按钮 - 固定在底部
                    Spacer()

                    bottomButtons
                        .padding(.horizontal)
                        .padding(.bottom, geometry.safeAreaInsets.bottom + 10)
                }
            )
            .overlay(
                // 图层控制按钮 - 悬浮在画布下方
                VStack {
                    Spacer()

                    if !processor.layers.isEmpty && processor.layers.contains(where: { $0.isSelected }) {
                        layerOrderButtons
                            .padding(.horizontal)
                            .padding(.bottom, geometry.safeAreaInsets.bottom + 100) // 在底部按钮上方悬浮
                    }
                }
            )
        }
        .ignoresSafeArea(.keyboard) // 全局忽略键盘安全区域
        .photosPicker(isPresented: $showingImagePicker, selection: $selectedPhoto, matching: .images)
        .onChange(of: selectedPhoto) { newValue in
            Task {
                await loadSelectedPhoto()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .layerTransformUpdated)) { notification in
            // 图层变换时触发自动保存
            if let layer = notification.object as? LayerModel {
                triggerAutoSave(actionDescription: "变换图层: \(layer.name)")
            }
        }
        .alert("保存成功", isPresented: $showingSaveAlert) {
            Button("确定") { }
        } message: {
            Text("图像已保存到相册")
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(activityItems: [generateCanvasImage()])
        }
        .sheet(isPresented: $showingHelp) {
            HelpView()
        }
        .sheet(isPresented: $showingTextEditor) {
            if let editingLayer = editingTextLayer {
                TextEditingView(layer: editingLayer, processor: processor, undoManager: undoManager)
            }
        }

    }

    // 白板编辑区域
    private var canvasArea: some View {
        GeometryReader { geometry in
            // 计算画布在可用空间中的居中位置
            let canvasX = (geometry.size.width - displayCanvasSize.width) / 2
            let canvasY = (geometry.size.height - displayCanvasSize.height) / 2

            ZStack {
                // 蓝色背景白板 - 使用显示尺寸，改为直角矩形确保与保存一致
                Rectangle()
                    .fill(CyberPunkStyle.electricBlue)
                    .frame(width: displayCanvasSize.width, height: displayCanvasSize.height)
                    .shadow(color: CyberPunkStyle.electricBlue.opacity(0.3), radius: 8, x: 0, y: 4)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                        .overlay(
                            // 图层容器 - 移除边界限制，允许图层超出背景
                            ZStack {
                                // 背景点击区域 - 用于取消选择
                                Color.clear
                                    .contentShape(Rectangle())
                                    .onTapGesture {
                                        // 点击空白区域取消选择并隐藏悬浮菜单
                                        processor.deselectAllLayers()
                                        processor.hideFloatingMenu()
                                        // 不再强制结束编辑模式，只能通过取消/确定按钮退出
                                    }



                                // 图层显示 - 移除边界限制
                                ForEach(processor.layers) { layer in
                                    LayerEditingView(
                                        layer: layer,
                                        processor: processor,
                                        undoManager: undoManager,
                                        canvasSize: displayCanvasSize
                                    )
                                }

                                // 空状态提示
                                if processor.layers.isEmpty && !processor.isProcessing {
                                    VStack(spacing: 15) {
                                        Image(systemName: "photo.badge.plus")
                                            .font(.system(size: 50))
                                            .foregroundColor(.white.opacity(0.7))

                                        VStack(spacing: 8) {
                                            Text("点击添加照片开始编辑")
                                                .font(.system(size: 16, weight: .medium))
                                                .foregroundColor(.white.opacity(0.9))

                                            Text("自动抠图，拖拽编辑")
                                                .font(.system(size: 13))
                                                .foregroundColor(.white.opacity(0.7))
                                        }
                                    }
                                }

                                // 画布调整指示器
                                if isResizingCanvas {
                                    VStack {
                                        Image(systemName: "aspectratio")
                                            .font(.system(size: 24))
                                            .foregroundColor(CyberPunkStyle.neonPink)
                                            .scaleEffect(1.2)

                                        Text("调整画布比例...")
                                            .font(.system(size: 12, weight: .medium))
                                            .foregroundColor(.white.opacity(0.8))
                                            .padding(.top, 4)
                                    }
                                    .padding()
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(Color.black.opacity(0.6))
                                            .blur(radius: 1)
                                    )
                                    .transition(.opacity.combined(with: .scale))
                                }
                            }
                            // 移除 .clipped() 以允许图层超出背景边界
                        )
            }
            .onAppear {
                updateCanvasSize()

                // 恢复项目数据
                restoreProjectData()
            }
            .onChange(of: selectedAspectRatio) { _ in
                // 标记开始调整尺寸
                isResizingCanvas = true

                // 使用动画让画布尺寸变化更平滑
                withAnimation(.easeInOut(duration: 0.3)) {
                    updateCanvasSize()
                }

                // 延迟重置状态
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    isResizingCanvas = false
                }
            }
            .overlay(
                // 全局悬浮菜单 - 相对于画布中心定位
                Group {
                    if processor.showingFloatingMenu,
                       let layer = processor.floatingMenuLayer {
                        // 将图层坐标转换为画布相对坐标
                        let canvasCenter = CGPoint(x: displayCanvasSize.width / 2, y: displayCanvasSize.height / 2)
                        let menuOffset = CGPoint(
                            x: processor.floatingMenuPosition.x,
                            y: processor.floatingMenuPosition.y
                        )

                        FloatingTextMenu(
                            layer: layer,
                            processor: processor,
                            undoManager: undoManager,
                            isVisible: .constant(true),
                            position: menuOffset
                        )
                        .zIndex(1000) // 确保在最顶层
                    }
                }
            )
        }
    }

    // 顶部工具栏 - 撤销/重做/保存
    private var topToolbar: some View {
        HStack {
            // 返回按钮（如果需要显示）
            if showBackButton {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 22, weight: .semibold))
                        .foregroundColor(CyberPunkStyle.neonPink)
                }
            } else {
                // 帮助按钮
                Button(action: {
                    showingHelp = true
                }) {
                    Image(systemName: "questionmark.circle")
                        .font(.system(size: 22))
                        .foregroundColor(CyberPunkStyle.neonPink)
                }
            }

            // 比例选择按钮
            Button(action: {
                showingAspectRatioMenu = true
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "aspectratio")
                        .font(.system(size: 16))
                    Text(selectedAspectRatio.rawValue)
                        .font(.system(size: 12, weight: .medium))
                }
                .foregroundColor(CyberPunkStyle.neonPink)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
            }
            .actionSheet(isPresented: $showingAspectRatioMenu) {
                ActionSheet(
                    title: Text("选择画布比例"),
                    buttons: AspectRatio.allCases.map { ratio in
                        .default(Text(ratio == selectedAspectRatio ? "✓ \(ratio.displayName)" : ratio.displayName)) {
                            if ratio != selectedAspectRatio {
                                selectedAspectRatio = ratio
                            }
                        }
                    } + [.cancel()]
                )
            }

            Spacer()

            // 调试按钮（仅在开发时显示）
            #if DEBUG
            Button(action: {
                debugSaveAndLoad()
            }) {
                Text("调试")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.orange)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(Color.orange.opacity(0.2))
                    )
            }
            #endif

            // 撤销/重做/保存按钮组
            if !processor.layers.isEmpty {
                HStack(spacing: 12) {
                    // 撤销按钮
                    Button(action: {
                        undoManager.undo(layers: &processor.layers)
                        Task {
                            await processor.updateCompositeImage()
                        }

                        // 触发自动保存
                        triggerAutoSave(actionDescription: "撤销操作")
                    }) {
                        Image(systemName: "arrow.uturn.backward")
                            .font(.system(size: 16))
                            .foregroundColor(.white)
                            .frame(width: 40, height: 40)
                            .background(
                                Circle()
                                    .fill(undoManager.canUndo ? CyberPunkStyle.decorativeBlue : Color.gray.opacity(0.5))
                                    .shadow(color: undoManager.canUndo ? CyberPunkStyle.decorativeBlue.opacity(0.5) : Color.clear, radius: 6)
                            )
                    }
                    .disabled(!undoManager.canUndo)

                    // 重做按钮
                    Button(action: {
                        undoManager.redo(layers: &processor.layers)
                        Task {
                            await processor.updateCompositeImage()
                        }

                        // 触发自动保存
                        triggerAutoSave(actionDescription: "重做操作")
                    }) {
                        Image(systemName: "arrow.uturn.forward")
                            .font(.system(size: 16))
                            .foregroundColor(.white)
                            .frame(width: 40, height: 40)
                            .background(
                                Circle()
                                    .fill(undoManager.canRedo ? CyberPunkStyle.decorativeBlue : Color.gray.opacity(0.5))
                                    .shadow(color: undoManager.canRedo ? CyberPunkStyle.decorativeBlue.opacity(0.5) : Color.clear, radius: 6)
                            )
                    }
                    .disabled(!undoManager.canRedo)

                    // 保存按钮
                    Button(action: {
                        saveCompositeImage()
                    }) {
                        Image(systemName: "square.and.arrow.down")
                            .font(.system(size: 16))
                            .foregroundColor(.white)
                            .frame(width: 40, height: 40)
                            .background(
                                Circle()
                                    .fill(CyberPunkStyle.neonPink)
                                    .shadow(color: CyberPunkStyle.neonPink.opacity(0.5), radius: 6)
                            )
                    }
                }
            }
        }
    }

    // 底部功能按钮
    private var bottomButtons: some View {
        HStack(spacing: 20) {
            // 添加照片按钮
            Button(action: {
                showingImagePicker = true
            }) {
                VStack(spacing: 8) {
                    Image(systemName: "photo.badge.plus")
                        .font(.system(size: 24))
                        .foregroundColor(.white)
                        .frame(width: 60, height: 60)
                        .background(
                            RoundedRectangle(cornerRadius: 15)
                                .fill(CyberPunkStyle.electricBlue)
                                .shadow(color: CyberPunkStyle.electricBlue.opacity(0.5), radius: 8)
                        )

                    Text("添加照片")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
            }

            // 添加背景文字按钮
            Button(action: {
                addDecorativeTextLayer()
            }) {
                VStack(spacing: 8) {
                    Image(systemName: "textformat.size")
                        .font(.system(size: 24))
                        .foregroundColor(.white)
                        .frame(width: 60, height: 60)
                        .background(
                            RoundedRectangle(cornerRadius: 15)
                                .fill(CyberPunkStyle.decorativeBlue)
                                .shadow(color: CyberPunkStyle.decorativeBlue.opacity(0.5), radius: 8)
                        )

                    Text("背景文字")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
            }

            // 添加标题文字按钮
            Button(action: {
                addTitleTextLayer()
            }) {
                VStack(spacing: 8) {
                    Image(systemName: "textformat")
                        .font(.system(size: 24))
                        .foregroundColor(.white)
                        .frame(width: 60, height: 60)
                        .background(
                            RoundedRectangle(cornerRadius: 15)
                                .fill(CyberPunkStyle.neonPink)
                                .shadow(color: CyberPunkStyle.neonPink.opacity(0.5), radius: 8)
                        )

                    Text("标题文字")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
            }
        }
    }

    // 图层顺序调整按钮
    private var layerOrderButtons: some View {
        HStack(spacing: 20) {
            Spacer()

            // 下移图层按钮
            Button(action: {
                if let selectedLayer = processor.layers.first(where: { $0.isSelected }) {
                    let oldIndex = processor.layers.firstIndex(where: { $0.id == selectedLayer.id }) ?? 0
                    processor.moveLayerDown(selectedLayer)
                    let newIndex = processor.layers.firstIndex(where: { $0.id == selectedLayer.id }) ?? 0
                    undoManager.recordAction(.moveLayer(selectedLayer, oldIndex, newIndex))

                    // 触发自动保存
                    triggerAutoSave(actionDescription: "下移图层")
                }
            }) {
                VStack(spacing: 4) {
                    Image(systemName: "arrow.down")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(width: 40, height: 40)
                        .background(
                            Circle()
                                .fill(CyberPunkStyle.decorativeBlue.opacity(0.8))
                                .shadow(color: CyberPunkStyle.decorativeBlue.opacity(0.5), radius: 6)
                        )

                    Text("下移")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
            }
            .disabled(!canMoveSelectedLayerDown())
            .opacity(canMoveSelectedLayerDown() ? 1.0 : 0.5)

            // 上移图层按钮
            Button(action: {
                if let selectedLayer = processor.layers.first(where: { $0.isSelected }) {
                    let oldIndex = processor.layers.firstIndex(where: { $0.id == selectedLayer.id }) ?? 0
                    processor.moveLayerUp(selectedLayer)
                    let newIndex = processor.layers.firstIndex(where: { $0.id == selectedLayer.id }) ?? 0
                    undoManager.recordAction(.moveLayer(selectedLayer, oldIndex, newIndex))

                    // 触发自动保存
                    triggerAutoSave(actionDescription: "上移图层")
                }
            }) {
                VStack(spacing: 4) {
                    Image(systemName: "arrow.up")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(width: 40, height: 40)
                        .background(
                            Circle()
                                .fill(CyberPunkStyle.decorativeBlue.opacity(0.8))
                                .shadow(color: CyberPunkStyle.decorativeBlue.opacity(0.5), radius: 6)
                        )

                    Text("上移")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
            }
            .disabled(!canMoveSelectedLayerUp())
            .opacity(canMoveSelectedLayerUp() ? 1.0 : 0.5)

            // 颜色变换按钮
            Button(action: {
                if let selectedLayer = processor.layers.first(where: { $0.isSelected }) {
                    let oldValue = selectedLayer.isColorTransformed
                    processor.toggleLayerColorTransform(selectedLayer)
                    undoManager.recordAction(.toggleLayerColorTransform(selectedLayer, oldValue))

                    // 触发自动保存
                    triggerAutoSave(actionDescription: "切换图层颜色")
                }
            }) {
                VStack(spacing: 4) {
                    Image(systemName: getSelectedLayer()?.isColorTransformed == true ? "paintpalette.fill" : "paintpalette")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(width: 40, height: 40)
                        .background(
                            Circle()
                                .fill((getSelectedLayer()?.isColorTransformed == true ? CyberPunkStyle.neonPink : CyberPunkStyle.decorativeBlue).opacity(0.8))
                                .shadow(color: (getSelectedLayer()?.isColorTransformed == true ? CyberPunkStyle.neonPink : CyberPunkStyle.decorativeBlue).opacity(0.5), radius: 6)
                        )

                    Text("变色")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
            }

            // 删除按钮
            Button(action: {
                if let selectedLayer = processor.layers.first(where: { $0.isSelected }) {
                    let originalIndex = processor.layers.firstIndex(where: { $0.id == selectedLayer.id }) ?? 0
                    undoManager.recordAction(.removeLayer(selectedLayer, originalIndex))
                    processor.removeLayer(selectedLayer)

                    // 触发自动保存
                    triggerAutoSave(actionDescription: "删除图层")
                }
            }) {
                VStack(spacing: 4) {
                    Image(systemName: "trash")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(width: 40, height: 40)
                        .background(
                            Circle()
                                .fill(Color.red.opacity(0.8))
                                .shadow(color: Color.red.opacity(0.5), radius: 6)
                        )

                    Text("删除")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
            }

            // 手动抠图按钮（只在选中图片图层且抠图失败时显示）
            if let selectedLayer = getSelectedLayer(),
               selectedLayer.type == .image,
               processor.lastCutoutFailed && processor.lastFailedLayer?.id == selectedLayer.id {
                Button(action: {
                    Task {
                        await processor.retryBackgroundRemoval(for: selectedLayer)
                    }
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: "scissors")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 40, height: 40)
                            .background(
                                Circle()
                                    .fill(Color.orange.opacity(0.8))
                                    .shadow(color: Color.orange.opacity(0.5), radius: 6)
                            )

                        Text("抠图")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
            }

            Spacer()
        }
    }

    // 控制按钮 (保留原有的，以防需要)
    private var controlButtons: some View {
        HStack(spacing: 15) {
            // 添加照片按钮
            Button(action: {
                showingImagePicker = true
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "photo.badge.plus")
                        .font(.system(size: 14))
                    Text("添加照片")
                        .font(.system(size: 14, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(CyberPunkStyle.electricBlue)
                        .shadow(color: CyberPunkStyle.electricBlue.opacity(0.5), radius: 8)
                )
            }

            // 添加文字按钮
            Button(action: {
                addTextLayer()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "textformat")
                        .font(.system(size: 14))
                    Text("添加文字")
                        .font(.system(size: 14, weight: .medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(CyberPunkStyle.neonPink)
                        .shadow(color: CyberPunkStyle.neonPink.opacity(0.5), radius: 8)
                )
            }

            // 撤销按钮
            Button(action: {
                undoManager.undo(layers: &processor.layers)
                Task {
                    await processor.updateCompositeImage()
                }
            }) {
                Image(systemName: "arrow.uturn.backward")
                    .font(.system(size: 14))
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(undoManager.canUndo ? CyberPunkStyle.decorativeBlue : Color.gray.opacity(0.5))
                            .shadow(color: undoManager.canUndo ? CyberPunkStyle.decorativeBlue.opacity(0.5) : Color.clear, radius: 8)
                    )
            }
            .disabled(!undoManager.canUndo)

            // 重做按钮
            Button(action: {
                undoManager.redo(layers: &processor.layers)
                Task {
                    await processor.updateCompositeImage()
                }
            }) {
                Image(systemName: "arrow.uturn.forward")
                    .font(.system(size: 14))
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(undoManager.canRedo ? CyberPunkStyle.decorativeBlue : Color.gray.opacity(0.5))
                            .shadow(color: undoManager.canRedo ? CyberPunkStyle.decorativeBlue.opacity(0.5) : Color.clear, radius: 8)
                    )
            }
            .disabled(!undoManager.canRedo)

            // 保存和分享按钮
            if !processor.layers.isEmpty {
                HStack(spacing: 12) {
                    // 保存按钮
                    Button(action: {
                        saveCompositeImage()
                    }) {
                        HStack(spacing: 6) {
                            Image(systemName: "square.and.arrow.down")
                                .font(.system(size: 14))
                            Text("保存")
                                .font(.system(size: 14, weight: .semibold))
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(CyberPunkStyle.neonPink)
                                .shadow(color: CyberPunkStyle.neonPink.opacity(0.5), radius: 8)
                        )
                    }

                    // 分享按钮
                    Button(action: {
                        showingShareSheet = true
                    }) {
                        Image(systemName: "square.and.arrow.up")
                            .font(.system(size: 14))
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 10)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(Color.gray.opacity(0.8))
                                    .shadow(color: Color.gray.opacity(0.5), radius: 8)
                            )
                    }
                }
            }
        }
    }
}

#Preview {
    ContentView(showBackButton: false)
}
