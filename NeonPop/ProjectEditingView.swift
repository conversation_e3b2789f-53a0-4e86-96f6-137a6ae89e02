//
//  ProjectEditingView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/16.
//

import SwiftUI
import Combine

struct ProjectEditingView: View {
    let project: Project?
    let projectManager: ProjectManager
    
    @Environment(\.presentationMode) var presentationMode
    @State private var contentView: ContentView?
    @State private var autoSaveTimer: Timer?
    @State private var lastSaveTime = Date()
    
    // 自动保存间隔（秒）
    private let autoSaveInterval: TimeInterval = 3.0
    
    init(project: Project? = nil, projectManager: ProjectManager) {
        self.project = project
        self.projectManager = projectManager
    }
    
    var body: some View {
        ZStack {
            if let contentView = contentView {
                contentView
                    .onAppear {
                        setupProject(contentView: contentView)
                        startAutoSave(contentView: contentView)
                    }
                    .onDisappear {
                        stopAutoSave()
                        // 最后保存一次
                        performAutoSave(contentView: contentView, actionDescription: "退出编辑")
                    }
            } else {
                // 加载中状态
                ZStack {
                    LinearGradient(
                        colors: [Color.black, CyberPunkStyle.electricBlue.opacity(0.3)],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                    .ignoresSafeArea()
                    
                    VStack(spacing: 16) {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: CyberPunkStyle.neonPink))
                            .scaleEffect(1.5)
                        
                        Text("正在加载项目...")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
            }
        }
        .onAppear {
            // 确定要使用的项目
            let targetProject: Project
            if let project = project {
                targetProject = project
                print("🔧 ProjectEditingView使用传入的项目: \(project.title) (ID: \(project.id))")
            } else if let currentProject = projectManager.currentProject {
                targetProject = currentProject
                print("🔧 ProjectEditingView使用当前项目: \(currentProject.title) (ID: \(currentProject.id))")
            } else {
                print("❌ ProjectEditingView没有找到目标项目")
                return
            }

            // 创建 ContentView 实例，直接传入项目信息
            contentView = ContentView(
                showBackButton: true,
                projectManager: projectManager,
                currentProject: targetProject
            )

            print("✅ ProjectEditingView ContentView创建完成")
        }
    }
    

    

    
    // 开始自动保存
    private func startAutoSave(contentView: ContentView) {
        autoSaveTimer = Timer.scheduledTimer(withTimeInterval: autoSaveInterval, repeats: true) { _ in
            performAutoSave(contentView: contentView, actionDescription: "自动保存")
        }
    }
    
    // 停止自动保存
    private func stopAutoSave() {
        autoSaveTimer?.invalidate()
        autoSaveTimer = nil
    }
    
    // 执行自动保存
    private func performAutoSave(contentView: ContentView, actionDescription: String) {
        guard let currentProject = contentView.currentProject else { return }
        
        // 检查是否有变化（简单检查图层数量和修改时间）
        let now = Date()
        if now.timeIntervalSince(lastSaveTime) < 1.0 {
            return // 避免过于频繁的保存
        }
        
        // 保存编辑步骤 - 通过ContentView的方法获取layers避免直接访问StateObject
        let layers = contentView.getLayersSafely()
        projectManager.saveEditStep(
            for: currentProject.id,
            actionDescription: actionDescription,
            layers: layers,
            canvasSize: contentView.canvasSize,
            aspectRatio: contentView.selectedAspectRatio
        )
        
        // 更新项目缩略图
        let thumbnailImage = contentView.generateCanvasImage()
        projectManager.updateProjectThumbnail(projectId: currentProject.id, image: thumbnailImage)
        
        lastSaveTime = now
        
        print("自动保存完成: \(actionDescription) - \(now)")
    }

    // 强制保存项目状态
    private func forceSaveProject(contentView: ContentView, project: Project) {
        print("💾 强制保存项目状态: \(project.title) (ID: \(project.id))")

        // 获取当前图层状态
        let layers = contentView.getLayersSafely()
        print("   当前图层数量: \(layers.count)")

        // 更新项目缩略图
        let thumbnailImage = contentView.generateCanvasImage()
        projectManager.updateProjectThumbnail(projectId: project.id, image: thumbnailImage)
        print("   ✅ 缩略图已更新")

        // 保存当前编辑步骤 - 通过ContentView的方法获取layers避免直接访问StateObject
        projectManager.saveEditStep(
            for: project.id,
            actionDescription: "项目状态保存",
            layers: layers,
            canvasSize: contentView.canvasSize,
            aspectRatio: contentView.selectedAspectRatio
        )

        print("✅ 强制保存项目完成: \(project.title)")

        // 验证保存结果
        let savedSteps = projectManager.getEditSteps(for: project.id)
        print("   验证: 项目现在有 \(savedSteps.count) 个编辑步骤")
        if let latestStep = savedSteps.last {
            print("   最新步骤: \(latestStep.actionDescription)，图层数: \(latestStep.layers.count)")
        }
    }
}


