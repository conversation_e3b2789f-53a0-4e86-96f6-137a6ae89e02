//
//  ProjectEditingView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/16.
//

import SwiftUI
import Combine

struct ProjectEditingView: View {
    let project: Project?
    let projectManager: ProjectManager
    
    @Environment(\.presentationMode) var presentationMode
    @State private var contentView: ContentView?
    @State private var autoSaveTimer: Timer?
    @State private var lastSaveTime = Date()
    
    // 自动保存间隔（秒）
    private let autoSaveInterval: TimeInterval = 3.0
    
    init(project: Project? = nil, projectManager: ProjectManager) {
        self.project = project
        self.projectManager = projectManager
    }
    
    var body: some View {
        ZStack {
            if let contentView = contentView {
                contentView
                    .onAppear {
                        setupProject(contentView: contentView)
                        startAutoSave(contentView: contentView)
                    }
                    .onDisappear {
                        stopAutoSave()
                        // 最后保存一次
                        performAutoSave(contentView: contentView, actionDescription: "退出编辑")
                    }
            } else {
                // 加载中状态
                ZStack {
                    LinearGradient(
                        colors: [Color.black, CyberPunkStyle.electricBlue.opacity(0.3)],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                    .ignoresSafeArea()
                    
                    VStack(spacing: 16) {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: CyberPunkStyle.neonPink))
                            .scaleEffect(1.5)
                        
                        Text("正在加载项目...")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
            }
        }
        .onAppear {
            // 创建 ContentView 实例
            contentView = ContentView(showBackButton: true)
        }
    }
    
    // 设置项目
    private func setupProject(contentView: ContentView) {
        print("🔧 开始设置项目")

        // 先设置项目管理器引用
        contentView.projectManager = projectManager

        // 确定要使用的项目 - 优先使用传入的project参数
        let targetProject: Project
        if let project = project {
            targetProject = project
            print("   使用传入的项目: \(project.title) (ID: \(project.id))")
        } else if let currentProject = projectManager.currentProject {
            targetProject = currentProject
            print("   使用当前项目: \(currentProject.title) (ID: \(currentProject.id))")
        } else {
            print("❌ 没有找到目标项目")
            return
        }

        // 重要：不要修改projectManager.currentProject，因为它可能被其他视图使用
        // 只设置contentView的currentProject
        contentView.currentProject = targetProject

        print("   ✅ 项目设置完成，ContentView项目: \(targetProject.title)")

        // 恢复项目数据
        restoreProject(targetProject, to: contentView)
    }
    
    // 恢复项目数据
    private func restoreProject(_ project: Project, to contentView: ContentView) {
        print("📂 开始恢复项目数据: \(project.title)")

        // 等待ContentView完全初始化后再进行恢复
        DispatchQueue.main.async {
            // 获取最新的编辑步骤
            let steps = self.projectManager.getEditSteps(for: project.id)
            print("   找到 \(steps.count) 个编辑步骤")

            if let latestStep = steps.last {
                print("   恢复最新步骤: \(latestStep.actionDescription)")
                print("   步骤图层数量: \(latestStep.layers.count)")

                // 恢复图层
                let restoredLayers = latestStep.restoreLayers()

                // 确保所有图层都不被选中（避免显示问题）
                for layer in restoredLayers {
                    layer.isSelected = false
                }

                contentView.processor.layers = restoredLayers
                print("   ✅ 图层恢复完成，实际图层数量: \(restoredLayers.count)")

                // 详细打印每个恢复的图层信息
                for (index, layer) in restoredLayers.enumerated() {
                    print("   图层\(index): \(layer.name)")
                    print("     类型: \(layer.type)")
                    print("     可见: \(layer.isVisible)")
                    print("     选中: \(layer.isSelected)")
                    if layer.type == .text {
                        print("     文字: \(layer.text ?? "nil")")
                        print("     字体大小: \(layer.fontSize)")
                        print("     颜色: \(layer.textColor)")
                        print("     模糊: \(layer.hasBlur), 半径: \(layer.blurRadius)")
                    }
                    if layer.type == .image {
                        print("     原始图片: \(layer.originalImage != nil ? "存在" : "nil")")
                        print("     处理图片: \(layer.processedImage != nil ? "存在" : "nil")")
                    }
                }

                // 恢复画布设置
                contentView.canvasSize = latestStep.canvasSize
                contentView.displayCanvasSize = latestStep.canvasSize

                // 恢复画布比例
                if let aspectRatio = AspectRatio.allCases.first(where: { $0.rawValue == latestStep.aspectRatio }) {
                    contentView.selectedAspectRatio = aspectRatio
                }
                print("   ✅ 画布设置恢复完成: \(latestStep.canvasSize)")
            } else {
                print("   没有编辑步骤，使用项目基本设置")
                // 如果没有步骤，使用项目的基本设置
                contentView.canvasSize = project.canvasSize
                contentView.displayCanvasSize = project.canvasSize

                if let aspectRatio = AspectRatio.allCases.first(where: { $0.rawValue == project.aspectRatio }) {
                    contentView.selectedAspectRatio = aspectRatio
                }

                // 清空图层，确保是空白项目
                contentView.processor.layers = []
                print("   ✅ 空白项目设置完成")
            }

            // 更新画布尺寸
            contentView.updateCanvasSize()

            // 更新合成图像 - 通过ContentView的方法来避免直接访问StateObject
            Task {
                await contentView.updateCompositeImageSafely()

                // 在合成图像更新完成后再更新缩略图和保存项目状态
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    // 强制保存一次项目状态，确保数据持久化
                    self.forceSaveProject(contentView: contentView, project: project)
                }
            }

            print("📂 项目数据恢复完成")
        }
    }
    
    // 开始自动保存
    private func startAutoSave(contentView: ContentView) {
        autoSaveTimer = Timer.scheduledTimer(withTimeInterval: autoSaveInterval, repeats: true) { _ in
            performAutoSave(contentView: contentView, actionDescription: "自动保存")
        }
    }
    
    // 停止自动保存
    private func stopAutoSave() {
        autoSaveTimer?.invalidate()
        autoSaveTimer = nil
    }
    
    // 执行自动保存
    private func performAutoSave(contentView: ContentView, actionDescription: String) {
        guard let currentProject = contentView.currentProject else { return }
        
        // 检查是否有变化（简单检查图层数量和修改时间）
        let now = Date()
        if now.timeIntervalSince(lastSaveTime) < 1.0 {
            return // 避免过于频繁的保存
        }
        
        // 保存编辑步骤 - 通过ContentView的方法获取layers避免直接访问StateObject
        let layers = contentView.getLayersSafely()
        projectManager.saveEditStep(
            for: currentProject.id,
            actionDescription: actionDescription,
            layers: layers,
            canvasSize: contentView.canvasSize,
            aspectRatio: contentView.selectedAspectRatio
        )
        
        // 更新项目缩略图
        let thumbnailImage = contentView.generateCanvasImage()
        projectManager.updateProjectThumbnail(projectId: currentProject.id, image: thumbnailImage)
        
        lastSaveTime = now
        
        print("自动保存完成: \(actionDescription) - \(now)")
    }

    // 强制保存项目状态
    private func forceSaveProject(contentView: ContentView, project: Project) {
        print("💾 强制保存项目状态: \(project.title) (ID: \(project.id))")

        // 获取当前图层状态
        let layers = contentView.getLayersSafely()
        print("   当前图层数量: \(layers.count)")

        // 更新项目缩略图
        let thumbnailImage = contentView.generateCanvasImage()
        projectManager.updateProjectThumbnail(projectId: project.id, image: thumbnailImage)
        print("   ✅ 缩略图已更新")

        // 保存当前编辑步骤 - 通过ContentView的方法获取layers避免直接访问StateObject
        projectManager.saveEditStep(
            for: project.id,
            actionDescription: "项目状态保存",
            layers: layers,
            canvasSize: contentView.canvasSize,
            aspectRatio: contentView.selectedAspectRatio
        )

        print("✅ 强制保存项目完成: \(project.title)")

        // 验证保存结果
        let savedSteps = projectManager.getEditSteps(for: project.id)
        print("   验证: 项目现在有 \(savedSteps.count) 个编辑步骤")
        if let latestStep = savedSteps.last {
            print("   最新步骤: \(latestStep.actionDescription)，图层数: \(latestStep.layers.count)")
        }
    }
}


